using AsyncAwaitBestPractices.MVVM;
using NSubstitute;
using Sanet.MakaMek.Core.Services;
using Sanet.MakaMek.Core.Services.Localization;
using Sanet.MakaMek.Presentation.ViewModels;
using Sanet.MVVM.Core.Services;
using Shouldly;

namespace Sanet.MakaMek.Presentation.Tests.ViewModels;

public class MainMenuViewModelTests
{
    private readonly MainMenuViewModel _sut;
    private readonly INavigationService _navigationService;
    private readonly IUnitCachingService _unitCachingService = Substitute.For<IUnitCachingService>();
    private readonly ILocalizationService _localizationService = Substitute.For<ILocalizationService>();

    public MainMenuViewModelTests()
    {
        _navigationService = Substitute.For<INavigationService>();

        // Setup default behavior for unit caching service
        _unitCachingService.GetAvailableModels().Returns(["LCT-1V", "SHD-2D"]);

        // Setup default behavior for localization service
        _localizationService.GetString("MainMenu_Loading_Content").Returns("Loading content...");
        _localizationService.GetString("MainMenu_Loading_NoItemsFound").Returns("No items found");
        _localizationService.GetString("MainMenu_Loading_ItemsLoaded").Returns("Loaded {0} items");
        _localizationService.GetString("MainMenu_Loading_Error").Returns("Error loading units: {0}");

        _sut = new MainMenuViewModel(_unitCachingService, _localizationService, 0);
        _sut.SetNavigationService(_navigationService);
    }

    [Fact]
    public void Constructor_InitializesCommands()
    {
        // Assert
        _sut.StartNewGameCommand.ShouldNotBeNull();
        _sut.JoinGameCommand.ShouldNotBeNull();
        _sut.Version.ShouldStartWith("v");
    }

    [Fact]
    public void Constructor_ShouldThrowArgumentNullException_WhenUnitCachingServiceIsNull()
    {
        // Arrange & Act & Assert
        Should.Throw<ArgumentNullException>(() => new MainMenuViewModel(null!, _localizationService));
    }

    [Fact]
    public void Constructor_ShouldThrowArgumentNullException_WhenLocalizationServiceIsNull()
    {
        // Arrange & Act & Assert
        Should.Throw<ArgumentNullException>(() => new MainMenuViewModel(_unitCachingService, null!));
    }

    [Fact]
    public async Task StartNewGameCommand_WhenExecuted_NavigatesToStartNewGameViewModel()
    {
        // Arrange
        var command = _sut.StartNewGameCommand as IAsyncCommand;
        command.ShouldNotBeNull();

        // Act
        await command.ExecuteAsync();

        // Assert
        await _navigationService.Received(1).NavigateToViewModelAsync<StartNewGameViewModel>();
    }
    
    [Fact]
    public async Task JoinGameCommand_WhenExecuted_NavigatesToJoinGameViewModel()
    {
        // Arrange
        var command = _sut.JoinGameCommand as IAsyncCommand;
        command.ShouldNotBeNull();

        // Act
        await command.ExecuteAsync();

        // Assert
        await _navigationService.Received(1).NavigateToViewModelAsync<JoinGameViewModel>();
    }
    
    [Fact]
    public async Task PreloadUnits_WhenExceptionThrown_SetsErrorTextAndKeepsLoadingTrue()
    {
        // Arrange
        const string errorMessage = "Test error message";
        _unitCachingService.When(x => x.GetAvailableModels())
            .Throw(new Exception(errorMessage));
            
        _sut.SetNavigationService(_navigationService);
        
        // Small delay to allow the background task to complete
        await Task.Delay(10);
        
        // Assert
        _sut.LoadingText.ShouldContain(errorMessage);
        _sut.IsLoading.ShouldBeTrue();
    }
    
    [Fact]
    public async Task PreloadUnits_WhenNoUnitsFound_SetsNoItemsFoundTextAndKeepsLoadingTrue()
    {
        // Arrange
        _unitCachingService.GetAvailableModels().Returns([]);
        
        _sut.SetNavigationService(_navigationService);
        
        // Small delay to allow the background task to complete
        await Task.Delay(10);
        
        // Assert
        _sut.LoadingText.ShouldContain("No items found");
        _sut.IsLoading.ShouldBeTrue();
    }   
}
