using Sanet.MakaMek.Core.Data.Units.Components;

namespace Sanet.MakaMek.Core.Models.Units.Components.Internal.Actuators;

public sealed class ShoulderActuator(ComponentData? componentData = null) : Component(Definition, componentData)
{
    public static readonly ActuatorDefinition Definition = new(
        "Shoulder",
        MakaMekComponent.Shoulder);

    public static readonly int[] DefaultMountSlots = [0];
}
