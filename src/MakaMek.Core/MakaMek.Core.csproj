<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <AssemblyName>Sanet.MakaMek.Core</AssemblyName>
        <RootNamespace>Sanet.MakaMek.Core</RootNamespace>
        <Description>An Attempt of Classic BattleTech game implementation. Game Logic</Description>
    </PropertyGroup>
    
    <ItemGroup>
        <None Include="../../README.md" Pack="true" PackagePath="" />
    </ItemGroup>
    
    <ItemGroup>
      <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.9" />
      <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.9" />
      <PackageReference Include="Sanet.MVVM.Core" Version="1.1.1" />
      <PackageReference Include="Sanet.Transport.Rx" Version="1.0.2" />
      <PackageReference Include="Sanet.Transport.SignalR.Client" Version="0.5.0" />
      <PackageReference Include="System.Reactive" Version="6.0.2" />
    </ItemGroup>
    
    <ItemGroup>
      <ProjectReference Include="..\MakaMek.SourceGenerators\MakaMek.SourceGenerators.csproj" OutputItemType="Analyzer" ReferenceOutputAssembly="false" />
    </ItemGroup>
    
    <ItemGroup>
      <Folder Include="Services\GitHub\" />
    </ItemGroup>

    <!-- Debug target to print properties -->
    <Target Name="PrintProperties" BeforeTargets="Build">
        <Message Importance="high" Text="Project: $(MSBuildProjectName)" />
        <Message Importance="high" Text="Version: $(Version)" />
        <Message Importance="high" Text="Authors: <AUTHORS>
        <Message Importance="high" Text="Company: $(Company)" />
    </Target>
</Project>
