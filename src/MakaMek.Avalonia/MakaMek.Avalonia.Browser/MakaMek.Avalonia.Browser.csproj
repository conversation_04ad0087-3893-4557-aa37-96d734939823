<Project Sdk="Microsoft.NET.Sdk.WebAssembly">
    <PropertyGroup>
        <TargetFramework>net9.0-browser</TargetFramework>
        <OutputType>Exe</OutputType>
        <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
        <AssemblyName>Sanet.MakaMek.Avalonia.Browser</AssemblyName>
        <RootNamespace>Sanet.MakaMek.Avalonia.Browser</RootNamespace>
        <JsonSerializerIsReflectionEnabledByDefault>true</JsonSerializerIsReflectionEnabledByDefault>
    </PropertyGroup>

    <PropertyGroup>
        <PublishTrimmed>true</PublishTrimmed>
        <TrimMode>partial</TrimMode>
        <TrimmerDefaultAction>link</TrimmerDefaultAction>
    </PropertyGroup>
    <ItemGroup>
        <!-- Preserve the entire command model namespace -->
        <TrimmerRootAssembly Include="Sanet.MakaMek.Core" />
        <TrimmerRootDescriptor Include="TrimmerRoots.xml" />
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="Avalonia.Browser" Version="11.3.5" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\MakaMek.Avalonia\MakaMek.Avalonia.csproj"/>
    </ItemGroup>
</Project>
