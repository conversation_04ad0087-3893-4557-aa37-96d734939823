<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:fragments="clr-namespace:Sanet.MakaMek.Avalonia.Views.JoinGame.Fragments"
             xmlns:fragments1="clr-namespace:Sanet.MakaMek.Avalonia.Views.NewGame.Fragments"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Sanet.MakaMek.Avalonia.Views.JoinGame.JoinGameViewNarrow"
             x:DataType="viewModels:JoinGameViewModel"
             xmlns:viewModels="clr-namespace:Sanet.MakaMek.Presentation.ViewModels;assembly=Sanet.MakaMek.Presentation"
             xmlns:fragments2="clr-namespace:Sanet.MakaMek.Avalonia.Views.StartNewGame.Fragments">

  <DockPanel Classes="pageContainer">
    <TextBlock Text="Join Game" 
               Classes="h1" 
               HorizontalAlignment="Center"
               DockPanel.Dock="Top"
               Margin="0,0,0,10"/>
    <ScrollViewer HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto">
      <StackPanel Spacing="15" Margin="10">
        <!-- Connect Fragment -->
        <Border Classes="card">
          <fragments:ConnectFragment DataContext="{Binding}"/>
        </Border>

        <!-- Players Fragment (Reused) -->
        <Border Classes="card">
            <fragments2:PlayersFragment DataContext="{Binding}"/>
        </Border>
      </StackPanel>
    </ScrollViewer>
  </DockPanel>
</UserControl>

