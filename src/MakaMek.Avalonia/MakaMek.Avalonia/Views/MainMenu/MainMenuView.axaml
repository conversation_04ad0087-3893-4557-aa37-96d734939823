<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:viewModels="clr-namespace:Sanet.MakaMek.Presentation.ViewModels;assembly=Sanet.MakaMek.Presentation"
             mc:Ignorable="d" d:DesignWidth="600" d:DesignHeight="400"
             x:DataType="viewModels:MainMenuViewModel"
             x:Class="Sanet.MakaMek.Avalonia.Views.MainMenu.MainMenuView">
 
    <Grid>
        <!-- Loading Panel -->
        <StackPanel HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Spacing="20"
                    IsVisible="{Binding IsLoading}">
            <ProgressBar IsIndeterminate="True"
                         Width="300"
                         Height="6"/>
            <TextBlock Text="{Binding LoadingText}"
                       Classes="body"
                       HorizontalAlignment="Center"/>
        </StackPanel>

        <!-- Centered Button Panel -->
        <StackPanel HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    Spacing="20"
                    IsVisible="{Binding !IsLoading}">
            <Button Content="Start New Game"
                    Classes="main-menu"
                    Command="{Binding StartNewGameCommand}"
                    HorizontalAlignment="Stretch"
                    MinWidth="200"/>
            <Button Content="Join Game"
                    Classes="main-menu"
                    Command="{Binding JoinGameCommand}"
                    HorizontalAlignment="Stretch"
                    MinWidth="200"/>
        </StackPanel>

        <!-- Version TextBlock -->
        <TextBlock Classes="body"
                   HorizontalAlignment="Right"
                   VerticalAlignment="Bottom"
                   Margin="10"
                   Text="{Binding Version}"/>

    </Grid>
</UserControl>

