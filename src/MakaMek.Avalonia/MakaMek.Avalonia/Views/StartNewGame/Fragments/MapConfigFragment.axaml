<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:viewModels1="clr-namespace:Sanet.MakaMek.Presentation.ViewModels;assembly=Sanet.MakaMek.Presentation"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="Sanet.MakaMek.Avalonia.Views.StartNewGame.Fragments.MapConfigFragment"
             x:DataType="viewModels1:StartNewGameViewModel">
    <StackPanel Classes="verticalSpaced">
        <StackPanel Classes="formGroup">
            <TextBlock Text="{Binding MapWidthLabel}" Classes="label"/>
            <Slider Value="{Binding MapWidth}" Minimum="8" Maximum="32" TickFrequency="1" IsSnapToTickEnabled="True" Classes="gameSlider"/>
            <TextBlock Text="{Binding MapWidth, StringFormat='Width: {0} hexes'}" Classes="bodySmall"/>
        </StackPanel>
        
        <StackPanel Classes="formGroup">
            <TextBlock Text="{Binding MapHeightLabel}" Classes="label"/>
            <Slider Value="{Binding MapHeight}" Minimum="8" Maximum="32" TickFrequency="1" IsSnapToTickEnabled="True" Classes="gameSlider"/>
            <TextBlock Text="{Binding MapHeight, StringFormat='Height: {0} hexes'}" Classes="bodySmall"/>
        </StackPanel>
        
        <StackPanel Classes="formGroup">
            <TextBlock Text="{Binding ForestCoverageLabel}" Classes="label"/>
            <Slider Value="{Binding ForestCoverage}" Minimum="0" Maximum="50" TickFrequency="5" Classes="gameSlider"/>
            <TextBlock Text="{Binding ForestCoverage, StringFormat='Forest Coverage: {0}%'}" Classes="bodySmall"/>
        </StackPanel>
        
        <StackPanel Classes="formGroup">
            <TextBlock Text="{Binding LightWoodsLabel}" Classes="label"/>
            <Slider Value="{Binding LightWoodsPercentage}" Minimum="0" Maximum="100" TickFrequency="5" IsEnabled="{Binding IsLightWoodsEnabled}" Classes="gameSlider"/>
            <TextBlock Text="{Binding LightWoodsPercentage, StringFormat='Light Woods: {0}%'}" Classes="bodySmall"/>
        </StackPanel>
    </StackPanel>
</UserControl>

