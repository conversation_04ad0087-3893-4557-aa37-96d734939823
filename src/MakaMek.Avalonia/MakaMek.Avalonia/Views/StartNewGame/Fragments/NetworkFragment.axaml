<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:viewModels1="clr-namespace:Sanet.MakaMek.Presentation.ViewModels;assembly=Sanet.MakaMek.Presentation"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:DataType="viewModels1:StartNewGameViewModel"
             x:Class="Sanet.MakaMek.Avalonia.Views.StartNewGame.Fragments.NetworkFragment">
    
    <StackPanel Classes="verticalSpaced">
        <TextBlock Text="Network Settings" Classes="h2" HorizontalAlignment="Center"/>
        
        <StackPanel Classes="formGroup">
            <TextBlock Text="Multiplayer" Classes="label"/>
        </StackPanel>
        
        <StackPanel Classes="formGroup">
            <TextBlock Text="Server Address" Classes="label"/>
            <Border Classes="card" Background="{DynamicResource OverlayTransparentBrush}" Padding="8,4" Margin="0">
                <SelectableTextBlock Text="{Binding ServerIpAddress}"/>
            </Border>
            <TextBlock Text="Share this address with other players to connect" 
                       Classes="bodySmall"
                       FontStyle="Italic"/>
        </StackPanel>
    </StackPanel>
</UserControl>

