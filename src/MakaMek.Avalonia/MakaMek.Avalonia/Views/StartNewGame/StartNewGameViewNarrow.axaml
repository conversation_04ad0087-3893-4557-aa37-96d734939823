<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:fragments="clr-namespace:Sanet.MakaMek.Avalonia.Views.NewGame.Fragments"
             xmlns:viewModels1="clr-namespace:Sanet.MakaMek.Presentation.ViewModels;assembly=Sanet.MakaMek.Presentation"
             xmlns:fragments1="clr-namespace:Sanet.MakaMek.Avalonia.Views.StartNewGame.Fragments"
             mc:Ignorable="d" d:DesignWidth="400" d:DesignHeight="800"
             x:Class="Sanet.MakaMek.Avalonia.Views.StartNewGame.StartNewGameViewNarrow"
             x:DataType="viewModels1:StartNewGameViewModel">
    <DockPanel Classes="pageContainer">
        <Button DockPanel.Dock="Bottom"
                Command="{Binding StartGameCommand}"
                Content="Start Game"
                Classes="primary"
                IsEnabled="{Binding CanStartGame}"
                HorizontalAlignment="Center"
                Margin="0,10,0,0"/>
        
        <TextBlock Text="Start New Game" 
                   Classes="h1" 
                   HorizontalAlignment="Center"
                   DockPanel.Dock="Top"
                   Margin="0,0,0,10"/>
        
        <TabControl>
            <TabItem Header="Map">
                <ScrollViewer Classes="gameScrollViewer">
                    <fragments1:MapConfigFragment/>
                </ScrollViewer>
            </TabItem>
            
            <TabItem Header="Network" IsVisible="{Binding CanStartLanServer}">
                <ScrollViewer Classes="gameScrollViewer">
                    <fragments1:NetworkFragment/>
                </ScrollViewer>
            </TabItem>
            
            <TabItem Header="Players">
                <ScrollViewer Classes="gameScrollViewer">
                    <fragments1:PlayersFragment/>
                </ScrollViewer>
            </TabItem>
        </TabControl>
    </DockPanel>
</UserControl>

