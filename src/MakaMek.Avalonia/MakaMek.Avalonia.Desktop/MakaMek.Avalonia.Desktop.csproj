<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <OutputType>WinExe</OutputType>
        <!--If you are willing to use Windows/MacOS native APIs you will need to create 3 projects.
        One for Windows with net9.0-windows TFM, one for MacOS with net9.0-macos and one with net9.0 TFM for Linux.-->
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
        <AssemblyName>MakaMek</AssemblyName>
        <RootNamespace>Sanet.MakaMek.Avalonia.Desktop</RootNamespace>
        <!-- Version information -->
        <Product>MakaMek</Product>
        <FileVersion>$([System.Text.RegularExpressions.Regex]::Match($(Version), ^\d+\.\d+\.\d+).Value)</FileVersion>
        <AssemblyVersion>$([System.Text.RegularExpressions.Regex]::Match($(Version), ^\d+\.\d+\.\d+).Value)</AssemblyVersion>
        <InformationalVersion>$(Version)</InformationalVersion>
        <ApplicationIcon>../MakaMek.Avalonia/Assets/avalonia-logo.ico</ApplicationIcon>
    </PropertyGroup>

    <PropertyGroup>
        <ApplicationManifest>app.manifest</ApplicationManifest>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Avalonia.Desktop" Version="11.3.5" />
        <!--Condition below is needed to remove Avalonia.Diagnostics package from build output in Release configuration.-->
        <PackageReference Condition="'$(Configuration)' == 'Debug'" Include="Avalonia.Diagnostics" Version="11.3.5" />
        <PackageReference Include="Sanet.Transport.SignalR.Server" Version="0.5.0" />
        <PackageReference Include="Velopack" Version="0.0.1298" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\MakaMek.Avalonia\MakaMek.Avalonia.csproj"/>
    </ItemGroup>
</Project>
