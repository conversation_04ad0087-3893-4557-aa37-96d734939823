<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net9.0-android</TargetFramework>
        <SupportedOSPlatformVersion>21</SupportedOSPlatformVersion>
        <Nullable>enable</Nullable>
        <ApplicationId>nl.sanetby.makamek</ApplicationId>
        <!-- Use version from Directory.Build.props -->
        <ApplicationVersion>$([MSBuild]::ValueOrDefault($([System.Text.RegularExpressions.Regex]::Match($(Version), ^\d+\.\d+\.\d+).Value.Replace('.', '')), 0))</ApplicationVersion>
        <ApplicationDisplayVersion>$(Version)</ApplicationDisplayVersion>
        <AndroidPackageFormat>apk</AndroidPackageFormat>
        <AndroidEnableProfiledAot>false</AndroidEnableProfiledAot>
        <AssemblyName>Sanet.MakaMek.Avalonia.Android</AssemblyName>
        <RootNamespace>Sanet.MakaMek.Avalonia.Android</RootNamespace>
    </PropertyGroup>

    <ItemGroup>
        <AndroidResource Include="Icon.png">
            <Link>Resources\drawable\Icon.png</Link>
        </AndroidResource>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Avalonia.Android" Version="11.3.5" />
        <PackageReference Include="Xamarin.AndroidX.Core.SplashScreen" Version="1.0.1.17" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\MakaMek.Avalonia\MakaMek.Avalonia.csproj"/>
    </ItemGroup>
</Project>
