<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <AssemblyName>MmuxPackager</AssemblyName>
        <RootNamespace>MakaMek.Tools.MmuxPackager</RootNamespace>
        <Description>CLI tool for packaging MakaMek unit data into distributable .mmux files</Description>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="System.CommandLine" Version="2.0.0-beta4.22272.1" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\MakaMek.Core\MakaMek.Core.csproj" />
    </ItemGroup>

</Project>
