<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <AssemblyName>MtfConverter</AssemblyName>
        <RootNamespace>MakaMek.Tools.MtfConverter</RootNamespace>
        <Description>CLI tool for converting MTF files to JSON format</Description>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="System.CommandLine" Version="2.0.0-beta4.22272.1" />
        <PackageReference Include="System.Text.Json" Version="9.0.9" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\MakaMek.Core\MakaMek.Core.csproj" />
    </ItemGroup>

</Project>
